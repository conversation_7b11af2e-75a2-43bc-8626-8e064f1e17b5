package utils

import play.api.Logger
import scalikejdbc.config.DBs
import akka.actor.ActorSystem
import akka.stream.ActorMaterializer
import api.AppConfig
import api.accounts.MicrosoftOAuth
import api.controllers.AuthController
import api.controllers.OAuthState
import api.dao.EmailHealthCheckDAO
import api.models.{Account, AccountDB, EmailAccount, EmailAccountForm, EmailAccountStatus, EmailScheduled, EmailServiceProvider, EmailSettings, EmailTestForm, EmailType, MsClientIdVersion}
import api.services.{AccountService, EmailAccountService, EmailHealthCheckRecordId, EmailHealthCheckService}
import org.joda.time.DateTime

import scala.concurrent.{Await, ExecutionContext, Future}
import play.api.libs.ws.ahc.AhcWSClient
import utils.cronjobs.{EmailReplyIngCronService, EmailSendingCronService}
import utils.email.{EmailAccountErrorHandlingService, EmailErrors, EmailHelper, GenericEmailService, GmailService, OutlookService, TEmailService}
import utils.helpers.LogHelpers
import utils.mq.{MQEmailSchedulerForLandingCheck, MQEmailScheduledForLandingCheck => LandingCheckMsg}
import utils.mq.email.{MQEmail, MQEmailMessage}

import java.time.LocalDateTime
import javax.mail.Message
import scala.concurrent.duration.Duration
import scala.util.{Failure, Success, Try}


object TestApp {
  def main(args: Array[String]): Unit = {

    implicit val Logger:SRLogger = new SRLogger("TestApp Checking Datta");

    if (args.length > 0) {

      DBs.setupAll()


      implicit val system: ActorSystem = ActorSystem()
      implicit val wSClient: AhcWSClient = AhcWSClient()
      implicit val actorContext: ExecutionContext = system.dispatcher


      val applicationName = args(0)
      applicationName match {


        case "vg/scheduleEmailAccountForSend" =>

          val resFut = EmailSendingCronService.scheduleEmailAccountForSend(
            emailAccountId = 4,
            Logger = Logger
          )

          val res = Await.result(resFut, scala.concurrent.duration.Duration.Inf)

          Logger.warn(s"scheduleEmailAccountForSend - $res")

        case "vg/mq_email_processMessage" =>

          val resFut = MQEmail.processMessage(
            msg = MQEmailMessage(emailScheduledId = 4)
          )

          val res = Await.result(resFut, scala.concurrent.duration.Duration.Inf)

          Logger.warn(s"MQEmail.processMessage - $res")

        case "vg/doLandingCheck" =>

          val resFut = MQEmailSchedulerForLandingCheck.processMessage(
            msg = LandingCheckMsg(emailScheduledId = 4)
          )

          val res = Await.result(resFut, scala.concurrent.duration.Duration.Inf)

          Logger.warn(s"doLandingCheck - $res")


        case "vg/schedule_for_reply" =>

          val resFut = for {

            foundEmailScheduledIdsForReply: List[Long] <- Future.fromTry(EmailScheduled.getAllEmailScheduledReadyForReply())

            updatedScheduledForReply: List[Long] <- Future.fromTry(EmailScheduled.updateScheduledForReply(emailScheduledIdsToBeUpdated = foundEmailScheduledIdsForReply))

            res <- Future.sequence(updatedScheduledForReply.map(esId => EmailReplyIngCronService.scheduleEmailAccountForReply(emailScheduledId = esId, Logger = Logger)))

          } yield {

            res

          }

          val res = Await.result(resFut, scala.concurrent.duration.Duration.Inf)

          Logger.warn(s"reply - $res")

        case "vg/reply" =>

          val resFut = EmailReplyIngCronService.scheduleEmailAccountForReply(emailScheduledId = 31, Logger = Logger)

          val res = Await.result(resFut, scala.concurrent.duration.Duration.Inf)

          Logger.warn(s"reply - $res")

        case "gu/getOauthUrl" =>

          // Use AppConfig.microsoftOAuthSettings, for SocialAuth
          val authorizationUrl = MicrosoftOAuth.authorizationUrl(
            state = "sfbgd",
            s = AppConfig.getMicrosoftOAuthSettings(isZapmailFlow = false, version = MsClientIdVersion.V1),
            emailAddress = Some("gfsjbv"),
            promptLogin = true
          )

          Logger.warn(authorizationUrl)

        case "vg/triggerEmailHealthCheck" =>

          val resFut = for {

            accountId: Long <- Try(args(1).toLong) match {

              case Failure(exception) =>

                Logger.error(
                  msg = s"Arg 1 - Invalid accountId: ${args.lift(1)}",
                  err = exception,
                )

                Future.failed(exception)

              case Success(value) =>

                Future.successful(value)

            }

            emailAccountId: Long <- Try(args(2).toLong) match {

              case Failure(exception) =>

                Logger.error(
                  msg = s"Arg 2 - Invalid emailAccountId: ${args.lift(2)}",
                  err = exception,
                )

                Future.failed(exception)

              case Success(value) =>

                Future.successful(value)

            }

            emailAccount: EmailAccount <- EmailAccount.findEmailAccounts(
              accountId = accountId,
              emailAccountIds = List(emailAccountId),
            ).map(_.headOption) match {

              case Failure(exception) =>

                println(s"Failed to find email account. accountId: $accountId :: emailAccountId: $emailAccountId :: ${LogHelpers.getStackTraceAsString(exception)}")

                Future.failed(exception)

              case Success(None) =>

                println(s"Failed to find email account - None. accountId: $accountId :: emailAccountId: $emailAccountId")

                Future.failed(new Exception("Email account not found"))

              case Success(Some(account)) =>

                Future.successful(account)

            }

            createdEmailHealthCheckRecordId: EmailHealthCheckRecordId <-
              EmailHealthCheckService.createEmailSettingHealthCheck(
                emailAccountForHealthCheck = emailAccount,
              )

          } yield {

            createdEmailHealthCheckRecordId

          }

          val res = Await.ready(resFut, scala.concurrent.duration.Duration.Inf)

          res.value match {

            case None =>

              Logger.error(s"NONE FOUND!")

            case Some(Failure(exception)) =>

              Logger.error(s"Failed. ${LogHelpers.getStackTraceAsString(exception)}")

            case Some(Success(emailHealthCheckRecordId)) =>

              Logger.info(s"Successfully created/updated email health check record - ${emailHealthCheckRecordId.id}")

          }

        case "vg/processPendingEmailHealthCheck" =>

          val tryOfPendingEmailHealthCheckIds = EmailHealthCheckDAO.getPendingEmailHealthChecksForProcessing

          tryOfPendingEmailHealthCheckIds match {

            case Failure(exception) =>

              println(s"Failed to get pending email health checks. ${LogHelpers.getStackTraceAsString(exception)}")

            case Success(emailHealthCheckRecordIds) =>

              emailHealthCheckRecordIds.foreach { id =>

                val resFut = EmailHealthCheckService.emailSettingHealthCheckTrack(
                  emailHealthCheckRecordId = id,
                )

                val res = Await.ready(resFut, scala.concurrent.duration.Duration.Inf)

                res.value match {

                  case None =>

                    Logger.error(s"NONE FOUND!")

                  case Some(Failure(exception)) =>

                    Logger.error(s"Failed. ${LogHelpers.getStackTraceAsString(exception)}")

                  case Some(Success(emailHealthCheckRecordId)) =>

                    Logger.info(s"Successfully tracked email health check record - ${emailHealthCheckRecordId.id}")

                }

              }

          }

        case "vg/pauseEmailAccountLandingError" =>

          EmailAccountErrorHandlingService.handleEmailAccountError(
            emailAccountId = 8,
            accountId = 1,
            emailAddress = "<EMAIL>",
            emailException = new Exception("Email Landing Check Failed."),
            Logger = Logger
          )(emailAccountError = EmailErrors.LandingCheckFailedExceededLimitError) match {
            case Failure(exception) =>

              Logger.error(s"handleEmailNotFoundForLandingCheckError Failed", err = exception)


            case Success(None) =>

              Logger.error(s"handleEmailNotFoundForLandingCheckError NONE")


            case Success(Some(_)) =>

              Logger.warn("Successful")

          }

        case "vg/clearError" =>

          EmailAccount.findReceiverEmailAccountForScheduling(
//            emailAccountIds = List(8),
//            accountId = 1,
            email_domain = "warmuplabs.com",
            senderEmailAccountId = 1
          ).map(_.headOption) match {
            case Failure(exception) =>

              Logger.error(s"clearError Failed", err = exception)


            case Success(None) =>

              Logger.error(s"clearError NONE")


            case Success(Some(ea)) =>

              Logger.warn(s"clearError Successful - ${ea.id}")

          }

        case "vg/findEmailScheduled" =>

          EmailScheduled.find(
            id = 29,
            Logger = Logger
          ) match {
            case Failure(exception) =>

              Logger.error(s"findEmailScheduled Failed", err = exception)

              throw exception

            case Success(None) =>

              Logger.error(s"findEmailScheduled NONE")

            case Success(Some(emailScheduled)) =>

              Logger.warn(s"findEmailScheduled Successful. emailScheduled: $emailScheduled")
          }

        case "vg/getCreditsBySRApiKey" =>

          val f = AccountDB.getCreditsBySRApiKey(
            sr_api_key = "sr_wrmpbx__Uvf8rLHCHdOwkWvUoDApTdu5Qg8MN2Rg",
            Logger = Logger
          )

          val fVal = Await.result(f, Duration.Inf)

          Logger.warn(s"Result $fVal")


        case "vg/resetSRCredits" =>

          val f = AccountDB.resetSRCredits(
            accountId = 1,
            total_active_warmup_emails = 3,
            sr_api_key = "someApiKey",
            Logger = Logger
          )

          val fVal = Await.result(f, Duration.Inf)

          Logger.warn(s"Result $fVal")

//        case "vg/parseHtmlBody" =>
//
//          val body =
//            """
//              |We are excited to introduce our latest innovation in the world of photography - a 35mm SLR camera that is set to revolutionize the way you capture life's precious moments. With its sleek design, advanced features, and unparalleled image quality, this camera is a game-changer for both professionals and enthusiasts alike.
//              |
//              |Our new camera boasts a range of innovative features, including a high-precision shutter, interchangeable lenses, and a built-in light meter. Whether you're a seasoned photographer or just starting out, this camera is designed to help you take your photography to the next level. We believe that this camera has the potential to make a significant impact on the photography industry, and we would love the opportunity to discuss how it can benefit your business.
//              |
//              |If you're interested in learning more, please don't hesitate to contact us.
//              |
//              |We look forward to hearing from you soon.
//              |
//              |""".stripMargin
//
//          val htmlBody = EmailHelper.makeHTMLBody(
//            baseBody = body,
//            salutation = "Hi Niklas, <br />",
//            signature = "Regards,<br />Johnny"
//          )
//
//          println(htmlBody)


        case "vg/searchEmailBody" =>

          val imap_settings = EmailSettings.ImapEmailAccount(
            imap_username = "<EMAIL>",
            imap_password = "muwv cwlo yhsq woka",
            imap_port = 993,
            imap_host = "imap.gmail.com"
          )


          val es = EmailScheduled(
            id = 34,
            sender_email_account_id = 34,
            from_email = "<EMAIL>",
            from_name = "Alaine Briseno",
            receiver_email_account_id = 34,
            to_email = "<EMAIL>",
            to_name = "Alaine Briseno",
            account_id = 23,
            body = "<EMAIL>",
            subject = "The watermelon is a squirrel | wrmpbx",
            template_id = 23,
            is_reply = false,
            message_id = None,
            warmup_email_account_id = 23,
            email_thread_id = "sdf",
            in_reply_to_header = None,
            references_header = None,
            sent_at = None,
            scheduled_at = Some(LocalDateTime.now().minusYears(1))
          )

          GmailService.fetchMessagesWithBodySearchTerm(
            bodySearchTerm = es.to_email,
            receiveCredentials = imap_settings,
            emailScheduled = es,
            Logger = Logger
          ).map { bounceCheckDetailsList =>

            bounceCheckDetailsList.foreach(bounceCheckDetails =>
              Logger.warn(
                s"Message Info: subject: ${bounceCheckDetails.subject} :: fromEmail: ${bounceCheckDetails.fromEmail} :: END_OF_MESSAGE"
              )
            )
          }

        case "vg/fetchAndUpdateOrgId" =>

          // sbt -J-Xmx4G -J-Xms4G "runMain utils.TestApp vg/fetchAndUpdateOrgId"

          /**
            * 10 Feb 2025
            *
            * We have some WH accounts which has the same API key - confirmed this with Prateek.
            *
            * Going forward, we want restrict 1 WH API key to 1 WH account.
            *
            * So, we are fetching the SmartReach `org_id` associated with the API key
            * of all WH accounts and store it in the newly created `org_id`` column
            * in the accounts table.
            *
            * Once the above migration is completed, we can again query the accounts
            * such that same API key is used in more than 1 WH account.
            *
            * Then we can reach out to such users.
            * We can take inputs from Prateek what to do with such accounts.
            */

          val f = AccountService.fetchAndUpdateOrgIdForAllAccounts

          Await.ready(f, Duration.Inf).value match {

            case None =>

              println("fetchAndUpdateOrgIdForAllAccounts - NONE FOUND!")

            case Some(Failure(exception)) =>

              println(s"fetchAndUpdateOrgIdForAllAccounts - failed. ${LogHelpers.getStackTraceAsString(exception)}")

            case Some(Success(value)) =>

              println(s"Success - Update count: ${value.count(_ == true)}")

          }


      }

    } else {
      Logger.error("Application name not provided")
    }
  }

}
